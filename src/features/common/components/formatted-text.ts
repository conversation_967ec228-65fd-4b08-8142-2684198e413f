import { LitElement, css, html } from 'lit';
import { customElement } from 'lit/decorators.js';

/**
 * Component for limiting the height of slot content with a fade overlay
 * Use CSS custom properties to control the fade effect:
 * - --fade-height: Height of the fade overlay (default: 0rem)
 * - --fade-color: Color to fade to (default: transparent)
 */
@customElement('height-limited-content')
export class HeightLimitedContent extends LitElement {
  static styles = css`
    :host {
      display: block;
      position: relative; /* Needed for absolute positioning of the overlay */
      overflow: hidden;
      padding-bottom: var(--fade-height, 0rem);
    }

    .overlay {
      position: absolute; /* Position over the slot */
      bottom: 0;
      left: 0;
      width: 100%;
      height: var(--fade-height, 0rem);
      background: linear-gradient(transparent, var(--fade-color, transparent));
    }
  `;

  @query('slot', true) slotContent!: HTMLSlotElement;

  private lastProcessedContent = '';

  private sanitize = () => {
    if (this.slotContent) {
      const assignedNodes = this.slotContent.assignedNodes();

      // Get current content to check if it has changed
      const currentContent = assignedNodes
        .map((node) => node.textContent || '')
        .join('');

      // Only process if content has actually changed
      if (currentContent === this.lastProcessedContent) {
        return;
      }

      this.lastProcessedContent = currentContent;

      assignedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // For element nodes, sanitize the HTML content
          const element = node as Element;
          const sanitizedHTML = formatText(element.outerHTML);
          replaceNodeWithSanitizedHTML(node, sanitizedHTML);
        } else if (node.nodeType === Node.TEXT_NODE) {
          // For text nodes, check if they contain HTML and process accordingly
          const textContent = node.textContent || '';

          // If text contains HTML tags, sanitize it
          if (/<[^>]+>/.test(textContent)) {
            const sanitizedHTML = formatText(textContent);
            replaceNodeWithSanitizedHTML(node, sanitizedHTML);
          }
          // Plain text nodes are left as-is (they're safe)
        }
      });
    }
  };

  firstUpdated() {
    if (this.slotContent) {
      this.slotContent.addEventListener('slotchange', this.sanitize);
    }
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    if (this.slotContent) {
      this.slotContent.removeEventListener('slotchange', this.sanitize);
    }
  }

  render() {
    return html`
      <slot></slot>
      <div class="overlay"></div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'formatted-text': FormattedText;
  }
}
